import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  Eye, 
  Heart, 
  Trophy, 
  DollarSign,
  Clock,
  Plus
} from 'lucide-react';
import { Auction, Bid, PageProps } from '../../types';
import AppLayout from '../../layouts/app-layout';
import AuctionCard from '../../components/auction/AuctionCard';
import { But<PERSON> } from '../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';

interface DashboardStatistics {
  active_auctions: number;
  total_auctions: number;
  active_bids: number;
  won_auctions: number;
}

interface Props extends PageProps {
  statistics: DashboardStatistics;
  recent_auctions: Auction[];
  recent_bids: Bid[];
  watched_auctions: Auction[];
}

const UserDashboard: React.FC<Props> = ({ 
  statistics, 
  recent_auctions, 
  recent_bids, 
  watched_auctions 
}) => {
  const statCards = [
    {
      title: 'Active Auctions',
      value: statistics.active_auctions,
      icon: Gavel,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      href: '/my/auctions?status=active',
    },
    {
      title: 'Total Auctions',
      value: statistics.total_auctions,
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      href: '/my/auctions',
    },
    {
      title: 'Active Bids',
      value: statistics.active_bids,
      icon: Eye,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      href: '/my/bids?status=winning',
    },
    {
      title: 'Won Auctions',
      value: statistics.won_auctions,
      icon: Trophy,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      href: '/my/won-auctions',
    },
  ];

  return (
    <AppLayout>
      <Head title="Dashboard" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-4">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back! Here's what's happening with your auctions and bids.
          </p>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statCards.map((stat) => {
            const Icon = stat.icon;
            return (
              <Link key={stat.title} href={stat.href}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">
                          {stat.title}
                        </p>
                        <p className="text-3xl font-bold text-foreground">
                          {stat.value}
                        </p>
                      </div>
                      <div className={`p-3 rounded-full ${stat.bgColor}`}>
                        <Icon className={`h-6 w-6 ${stat.color}`} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Auctions */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Gavel className="h-5 w-5 mr-2" />
                  Recent Auctions
                </CardTitle>
                <Link href="/my/auctions">
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {recent_auctions.length > 0 ? (
                <div className="space-y-4">
                  {recent_auctions.map((auction) => (
                    <div key={auction.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="w-16 h-16 bg-muted rounded overflow-hidden flex-shrink-0">
                        {auction.images[0] ? (
                          <img
                            src={auction.images[0].thumbnail_url || auction.images[0].url}
                            alt={auction.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Gavel className="h-6 w-6 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <Link href={`/auctions/${auction.id}`} className="block">
                          <h4 className="font-medium text-foreground truncate hover:text-primary">
                            {auction.title}
                          </h4>
                        </Link>
                        <div className="flex items-center space-x-4 mt-1 text-sm text-muted-foreground">
                          <Badge variant="outline">{auction.status}</Badge>
                          <span>{auction.current_bid.formatted}</span>
                          <span>{auction.statistics.bids_count} bids</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Gavel className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                  <p className="text-muted-foreground mb-4">No auctions yet</p>
                  <Link href="/auctions/create">
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Your First Auction
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Bids */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Recent Bids
                </CardTitle>
                <Link href="/my/bids">
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {recent_bids.length > 0 ? (
                <div className="space-y-4">
                  {recent_bids.map((bid) => (
                    <div key={bid.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="w-16 h-16 bg-muted rounded overflow-hidden flex-shrink-0">
                        {bid.auction?.images[0] ? (
                          <img
                            src={bid.auction.images[0].thumbnail_url || bid.auction.images[0].url}
                            alt={bid.auction.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Gavel className="h-6 w-6 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        {bid.auction && (
                          <Link href={`/auctions/${bid.auction.id}`} className="block">
                            <h4 className="font-medium text-foreground truncate hover:text-primary">
                              {bid.auction.title}
                            </h4>
                          </Link>
                        )}
                        <div className="flex items-center space-x-4 mt-1 text-sm">
                          <span className="font-medium text-primary">
                            {bid.amount.formatted}
                          </span>
                          {bid.status.is_winning ? (
                            <Badge variant="default" className="bg-green-100 text-green-800">
                              Winning
                            </Badge>
                          ) : bid.status.is_outbid ? (
                            <Badge variant="destructive">
                              Outbid
                            </Badge>
                          ) : (
                            <Badge variant="secondary">
                              Active
                            </Badge>
                          )}
                          <span className="text-muted-foreground">
                            {bid.timing.time_ago}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                  <p className="text-muted-foreground mb-4">No bids placed yet</p>
                  <Link href="/auctions">
                    <Button variant="outline">
                      Browse Auctions
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Watched Auctions */}
        {watched_auctions.length > 0 && (
          <div className="mt-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-foreground flex items-center">
                <Heart className="h-6 w-6 mr-2" />
                Watched Auctions
              </h2>
              <Link href="/watchlist">
                <Button variant="outline">View All</Button>
              </Link>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {watched_auctions.slice(0, 4).map((auction) => (
                <AuctionCard key={auction.id} auction={auction} />
              ))}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-8">
          <h2 className="text-2xl font-bold text-foreground mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link href="/auctions/create">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <Plus className="h-8 w-8 mx-auto mb-3 text-primary" />
                  <h3 className="font-semibold mb-2">Create Auction</h3>
                  <p className="text-sm text-muted-foreground">
                    List a new item for auction
                  </p>
                </CardContent>
              </Card>
            </Link>
            
            <Link href="/auctions">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <Eye className="h-8 w-8 mx-auto mb-3 text-primary" />
                  <h3 className="font-semibold mb-2">Browse Auctions</h3>
                  <p className="text-sm text-muted-foreground">
                    Find items to bid on
                  </p>
                </CardContent>
              </Card>
            </Link>
            
            <Link href="/my/profile">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <DollarSign className="h-8 w-8 mx-auto mb-3 text-primary" />
                  <h3 className="font-semibold mb-2">Manage Profile</h3>
                  <p className="text-sm text-muted-foreground">
                    Update your account settings
                  </p>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default UserDashboard;
