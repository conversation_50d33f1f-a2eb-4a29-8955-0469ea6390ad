import React from 'react';
import { <PERSON> } from '@inertiajs/react';
import { Heart, Eye, Clock, Gavel } from 'lucide-react';
import { Auction } from '../../types';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';
import { router } from '@inertiajs/react';

interface AuctionCardProps {
  auction: Auction;
  onWatchToggle?: (auctionId: number, isWatched: boolean) => void;
  className?: string;
}

const AuctionCard: React.FC<AuctionCardProps> = ({ 
  auction, 
  onWatchToggle,
  className 
}) => {
  const primaryImage = auction.images.find(img => img.is_primary) || auction.images[0];
  const isEndingSoon = auction.timing.is_ending_soon;
  const timeRemaining = auction.timing.remaining_minutes;
  
  const handleWatchToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (auction.metadata.is_watched) {
      router.delete(`/watchlist/${auction.id}`, {
        preserveScroll: true,
        onSuccess: () => {
          if (onWatchToggle) {
            onWatchToggle(auction.id, false);
          }
        }
      });
    } else {
      router.post('/watchlist', 
        { auction_id: auction.id },
        {
          preserveScroll: true,
          onSuccess: () => {
            if (onWatchToggle) {
              onWatchToggle(auction.id, true);
            }
          }
        }
      );
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'ended':
        return 'secondary';
      case 'scheduled':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const formatTimeRemaining = (endTime: string): string => {
    const now = new Date().getTime();
    const end = new Date(endTime).getTime();
    const difference = end - now;

    if (difference <= 0) return 'Ended';

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  return (
    <Card className={cn('group hover:shadow-lg transition-all duration-200', className)}>
      <Link href={`/auctions/${auction.id}`} className="block">
        {/* Image */}
        <div className="relative aspect-[4/3] overflow-hidden rounded-t-lg">
          {primaryImage ? (
            <img
              src={primaryImage.thumbnail_url || primaryImage.url}
              alt={primaryImage.alt_text || auction.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Gavel className="h-12 w-12 text-muted-foreground" />
            </div>
          )}
          
          {/* Status Badge */}
          <div className="absolute top-3 left-3">
            <Badge variant={getStatusVariant(auction.status)} className="text-xs">
              {auction.status.charAt(0).toUpperCase() + auction.status.slice(1)}
            </Badge>
          </div>

          {/* Featured Badge */}
          {auction.features.is_featured && (
            <div className="absolute top-3 right-3">
              <Badge variant="default" className="text-xs">
                Featured
              </Badge>
            </div>
          )}

          {/* Watch Button */}
          <button
            onClick={handleWatchToggle}
            className="absolute bottom-3 right-3 p-2 bg-white/90 hover:bg-white rounded-full shadow-sm transition-colors"
          >
            <Heart 
              className={cn(
                'h-4 w-4',
                auction.metadata.is_watched 
                  ? 'fill-red-500 text-red-500' 
                  : 'text-muted-foreground'
              )}
            />
          </button>

          {/* Ending Soon Indicator */}
          {isEndingSoon && auction.status === 'active' && (
            <div className="absolute bottom-3 left-3">
              <Badge variant="destructive" className="text-xs">
                <Clock className="h-3 w-3 mr-1" />
                Ending Soon
              </Badge>
            </div>
          )}
        </div>

        <CardContent className="p-4">
          {/* Title */}
          <h3 className="font-semibold text-lg text-foreground mb-2 line-clamp-2 group-hover:text-primary transition-colors">
            {auction.title}
          </h3>

          {/* Price Information */}
          <div className="space-y-2 mb-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Current Bid</span>
              <span className="font-bold text-lg text-primary">
                {auction.current_bid.formatted}
              </span>
            </div>
            
            {auction.reserve_price && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Reserve</span>
                <span className={cn(
                  'font-medium',
                  auction.reserve_price.is_met ? 'text-green-600' : 'text-yellow-600'
                )}>
                  {auction.reserve_price.is_met ? 'Met' : 'Not Met'}
                </span>
              </div>
            )}

            {auction.buyout_price && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Buy Now</span>
                <span className="font-medium text-foreground">
                  {auction.buyout_price.formatted}
                </span>
              </div>
            )}
          </div>

          {/* Time Remaining */}
          {auction.status === 'active' && timeRemaining !== null && (
            <div className="mb-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Time Left</span>
                <span className={cn(
                  'font-medium',
                  isEndingSoon ? 'text-red-600' : 'text-foreground'
                )}>
                  {timeRemaining > 0 ? formatTimeRemaining(auction.timing.end_time) : 'Ended'}
                </span>
              </div>
            </div>
          )}

          {/* Statistics */}
          <div className="flex items-center justify-between text-sm text-muted-foreground pt-3 border-t">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <Gavel className="h-4 w-4 mr-1" />
                <span>{auction.statistics.bids_count}</span>
              </div>
              <div className="flex items-center">
                <Eye className="h-4 w-4 mr-1" />
                <span>{auction.statistics.views_count}</span>
              </div>
              <div className="flex items-center">
                <Heart className="h-4 w-4 mr-1" />
                <span>{auction.statistics.watchers_count}</span>
              </div>
            </div>
            
            {auction.seller && (
              <div className="text-right">
                <div className="font-medium text-foreground">
                  {auction.seller.name}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Link>
    </Card>
  );
};

export default AuctionCard;
