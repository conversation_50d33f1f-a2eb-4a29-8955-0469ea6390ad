<?php

declare(strict_types=1);

namespace App\Http\Controllers\Web;

use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\Repositories\CategoryRepositoryInterface;
use App\Domain\Shared\ValueObjects\Id;
use App\Http\Controllers\Controller;
use App\Http\Resources\AuctionResource;
use App\Http\Resources\CategoryResource;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class CategoryController extends Controller
{
    public function __construct(
        private CategoryRepositoryInterface $categoryRepository,
        private AuctionRepositoryInterface $auctionRepository
    ) {}

    /**
     * Display a listing of categories
     */
    public function index(): Response
    {
        $categories = $this->categoryRepository->findAllWithHierarchy();

        return Inertia::render('Categories/Index', [
            'categories' => CategoryResource::collection($categories),
        ]);
    }

    /**
     * Display the specified category and its auctions
     */
    public function show(int $id, Request $request): Response
    {
        $category = $this->categoryRepository->findByIdOrFail(Id::fromString((string) $id));
        
        $perPage = min($request->get('per_page', 15), 50);
        $status = $request->get('status');
        $search = $request->get('search');
        $sortBy = $request->get('sort_by', 'ending_soon');

        $filters = [
            'status' => $status,
            'search' => $search,
            'sort_by' => $sortBy,
        ];

        $auctions = $this->auctionRepository->findByCategory(
            Id::fromString((string) $id),
            $perPage,
            $filters
        );

        // Get subcategories
        $subcategories = $this->categoryRepository->findChildren(Id::fromString((string) $id));

        return Inertia::render('Categories/Show', [
            'category' => new CategoryResource($category),
            'auctions' => AuctionResource::collection($auctions),
            'subcategories' => CategoryResource::collection($subcategories),
            'filters' => array_merge($filters, ['per_page' => $perPage]),
        ]);
    }

    /**
     * Display auctions for a category (AJAX for filtering)
     */
    public function auctions(int $id, Request $request): Response
    {
        $perPage = min($request->get('per_page', 15), 50);
        $status = $request->get('status');
        $search = $request->get('search');
        $sortBy = $request->get('sort_by', 'ending_soon');

        $filters = [
            'status' => $status,
            'search' => $search,
            'sort_by' => $sortBy,
        ];

        $auctions = $this->auctionRepository->findByCategory(
            Id::fromString((string) $id),
            $perPage,
            $filters
        );

        return Inertia::render('Categories/Auctions', [
            'auctions' => AuctionResource::collection($auctions),
            'filters' => array_merge($filters, ['per_page' => $perPage]),
        ]);
    }
}
